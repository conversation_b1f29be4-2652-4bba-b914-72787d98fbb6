#!/usr/bin/env python3
"""
Critical Assessment: Scalability & Resource Limits Tests

This module tests for scalability issues in the streaming JSON reader:
1. Behavior with extremely large files (>100GB simulation)
2. Performance with high concurrency
3. Resource exhaustion scenarios
4. Timeout handling for long-running operations
5. Memory pressure under system constraints
"""

import pytest
import duckdb
import json
import psutil
import os
import tempfile
import time
import threading
import multiprocessing
import shutil
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed


class TestScalabilityAndResourceLimits:
    """Test suite for scalability and resource limit handling."""
    
    @pytest.fixture(scope="class")
    def duckdb_connection(self):
        """Create a DuckDB connection with the streaming JSON reader extension loaded."""
        conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
        
        # Load the extension
        extension_path = "../build/debug/streaming_json_reader.duckdb_extension"
        if not Path(extension_path).exists():
            pytest.skip(f"Extension not found at {extension_path}. Run 'make debug' first.")

        conn.execute(f'LOAD "{extension_path}"')
        return conn
    
    def get_system_resources(self):
        """Get current system resource usage."""
        memory = psutil.virtual_memory()
        disk = shutil.disk_usage('.')
        cpu_count = psutil.cpu_count()
        
        return {
            'memory_total_gb': memory.total / (1024**3),
            'memory_available_gb': memory.available / (1024**3),
            'memory_percent': memory.percent,
            'disk_free_gb': disk.free / (1024**3),
            'cpu_count': cpu_count,
            'cpu_percent': psutil.cpu_percent(interval=1)
        }
    
    def create_large_file_simulation(self, target_size_mb=1000, chunk_size=1000):
        """Create a large JSON file by writing in chunks to simulate very large files."""
        temp_file = tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False)
        
        try:
            # Start JSON structure
            temp_file.write('{"users": [')
            
            bytes_written = len('{"users": [')
            target_bytes = target_size_mb * 1024 * 1024
            user_id = 0
            
            while bytes_written < target_bytes:
                # Write a chunk of users
                chunk_users = []
                for i in range(chunk_size):
                    user = {
                        "id": user_id,
                        "name": f"User_{user_id}",
                        "email": f"user{user_id}@example.com",
                        "department": "Engineering" if user_id % 2 == 0 else "Design",
                        "bio": "A" * 100,  # 100 byte bio
                        "projects": [
                            {
                                "name": f"Project_{user_id}_{j}",
                                "status": "active",
                                "budget": 10000 + (user_id * j * 100),
                                "description": "B" * 50  # 50 byte description
                            }
                            for j in range(3)  # 3 projects per user
                        ]
                    }
                    chunk_users.append(user)
                    user_id += 1
                
                # Write chunk
                if bytes_written > len('{"users": ['):
                    temp_file.write(',')
                
                chunk_json = json.dumps(chunk_users)[1:-1]  # Remove array brackets
                temp_file.write(chunk_json)
                
                bytes_written += len(chunk_json) + 1  # +1 for comma
                
                # Check if we should stop
                if bytes_written >= target_bytes:
                    break
            
            # Close JSON structure
            temp_file.write(']}')
            temp_file.close()
            
            return temp_file.name, user_id
            
        except Exception as e:
            temp_file.close()
            try:
                os.unlink(temp_file.name)
            except:
                pass
            raise e
    
    def test_large_file_handling(self, duckdb_connection):
        """Test behavior with large files (simulating >1GB files)."""
        print("\n=== Testing Large File Handling ===")
        
        resources = self.get_system_resources()
        print(f"System resources: {resources['memory_available_gb']:.1f}GB RAM, {resources['disk_free_gb']:.1f}GB disk")
        
        # Test progressively larger files
        file_sizes = [100, 500, 1000]  # MB
        if resources['disk_free_gb'] > 5:
            file_sizes.append(2000)  # 2GB if we have space
        
        for size_mb in file_sizes:
            print(f"Testing {size_mb}MB file")
            
            if resources['disk_free_gb'] < (size_mb / 1024) * 2:  # Need 2x space for safety
                print(f"  Skipping {size_mb}MB test - insufficient disk space")
                continue
            
            try:
                # Create large file
                start_time = time.time()
                large_file, user_count = self.create_large_file_simulation(size_mb, 1000)
                creation_time = time.time() - start_time
                
                file_size_actual = os.path.getsize(large_file) / (1024 * 1024)
                print(f"  Created {file_size_actual:.1f}MB file with {user_count} users in {creation_time:.1f}s")
                
                try:
                    # Test streaming reader
                    mem_before = psutil.virtual_memory().used / (1024**3)
                    start_time = time.time()
                    
                    result = duckdb_connection.execute(
                        f"SELECT COUNT(*) FROM streaming_json_reader('{large_file}')"
                    ).fetchone()
                    
                    end_time = time.time()
                    mem_after = psutil.virtual_memory().used / (1024**3)
                    
                    execution_time = end_time - start_time
                    memory_used = mem_after - mem_before
                    
                    print(f"  Results: {result[0]} rows, {execution_time:.1f}s, {memory_used:.2f}GB memory")
                    
                    # Verify streaming behavior (memory should not grow with file size)
                    max_memory_gb = min(2.0, resources['memory_available_gb'] * 0.1)  # Max 2GB or 10% of available
                    assert memory_used < max_memory_gb, f"Memory usage too high: {memory_used:.2f}GB"
                    
                    # Performance should be reasonable
                    max_time = size_mb * 0.1  # 0.1 seconds per MB
                    assert execution_time < max_time, f"Processing too slow: {execution_time:.1f}s for {size_mb}MB"
                    
                finally:
                    os.unlink(large_file)
                    
            except Exception as e:
                print(f"  Large file test failed: {str(e)[:100]}...")
                if "disk" in str(e).lower() or "space" in str(e).lower():
                    print("  Skipping remaining large file tests due to disk space")
                    break
                continue
    
    def test_high_concurrency_performance(self, duckdb_connection):
        """Test performance with high concurrency."""
        print("\n=== Testing High Concurrency Performance ===")
        
        # Create a test file
        test_data = {"users": [{"id": i, "name": f"User_{i}"} for i in range(1000)]}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # Test different concurrency levels
            concurrency_levels = [1, 2, 4, 8, 16]
            max_workers = min(16, psutil.cpu_count() * 2)
            
            for num_threads in concurrency_levels:
                if num_threads > max_workers:
                    continue
                    
                print(f"Testing {num_threads} concurrent threads")
                
                results = []
                errors = []
                start_time = time.time()
                
                def worker_query():
                    try:
                        # Each thread gets its own connection to avoid conflicts
                        local_conn = duckdb.connect(config={'allow_unsigned_extensions': 'true'})
                        local_conn.execute('LOAD "../build/debug/streaming_json_reader.duckdb_extension"')
                        
                        result = local_conn.execute(
                            f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                        ).fetchone()
                        
                        local_conn.close()
                        return result[0]
                        
                    except Exception as e:
                        return str(e)
                
                with ThreadPoolExecutor(max_workers=num_threads) as executor:
                    futures = [executor.submit(worker_query) for _ in range(num_threads)]
                    
                    for future in as_completed(futures):
                        try:
                            result = future.result(timeout=30)
                            if isinstance(result, int):
                                results.append(result)
                            else:
                                errors.append(result)
                        except Exception as e:
                            errors.append(str(e))
                
                end_time = time.time()
                total_time = end_time - start_time
                
                print(f"  {num_threads} threads: {len(results)} success, {len(errors)} errors, {total_time:.2f}s")
                
                # Most queries should succeed
                success_rate = len(results) / num_threads
                assert success_rate > 0.8, f"Low success rate: {success_rate:.2f}"
                
                # Performance should not degrade severely with concurrency
                if num_threads == 1:
                    baseline_time = total_time
                else:
                    # Allow some degradation but not exponential
                    max_expected_time = baseline_time * (num_threads ** 0.5)
                    assert total_time < max_expected_time * 2, f"Severe performance degradation: {total_time:.2f}s"
                
        finally:
            os.unlink(temp_file)
    
    def test_resource_exhaustion_scenarios(self, duckdb_connection):
        """Test behavior under resource exhaustion scenarios."""
        print("\n=== Testing Resource Exhaustion Scenarios ===")
        
        resources = self.get_system_resources()
        
        # Test 1: Memory pressure simulation
        print("Testing memory pressure resistance")
        
        # Create a file that would use significant memory if not streamed
        large_data = {
            "users": [
                {
                    "id": i,
                    "name": f"User_{i}",
                    "bio": "A" * 10000,  # 10KB per user
                    "projects": [
                        {
                            "name": f"Project_{i}_{j}",
                            "description": "B" * 5000  # 5KB per project
                        }
                        for j in range(10)  # 10 projects per user
                    ]
                }
                for i in range(100)  # 100 users = ~5MB per user = 500MB total
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(large_data, f)
            temp_file = f.name
        
        try:
            # Monitor memory during processing
            mem_samples = []
            
            def memory_monitor():
                for _ in range(100):  # Monitor for 10 seconds
                    mem_samples.append(psutil.virtual_memory().used / (1024**3))
                    time.sleep(0.1)
            
            monitor_thread = threading.Thread(target=memory_monitor)
            monitor_thread.start()
            
            result = duckdb_connection.execute(
                f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
            ).fetchone()
            
            monitor_thread.join()
            
            if mem_samples:
                min_memory = min(mem_samples)
                max_memory = max(mem_samples)
                memory_variance = max_memory - min_memory
                
                print(f"  Memory variance during processing: {memory_variance:.2f}GB")
                print(f"  Result: {result[0]} rows processed")
                
                # Memory variance should be small (streaming behavior)
                assert memory_variance < 1.0, f"High memory variance suggests non-streaming: {memory_variance:.2f}GB"
            
        finally:
            os.unlink(temp_file)
    
    def test_timeout_handling(self, duckdb_connection):
        """Test timeout handling for long-running operations."""
        print("\n=== Testing Timeout Handling ===")
        
        # Create a file designed to take some time to process
        slow_data = {
            "users": [
                {
                    "id": i,
                    "name": f"User_{i}",
                    "data": "X" * 1000,  # 1KB per field
                    "projects": [
                        {
                            "name": f"Project_{i}_{j}",
                            "description": "Y" * 1000,
                            "metadata": {
                                "field1": "Z" * 100,
                                "field2": "W" * 100,
                                "field3": "V" * 100
                            }
                        }
                        for j in range(50)  # 50 projects per user
                    ]
                }
                for i in range(200)  # 200 users
            ]
        }
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(slow_data, f)
            temp_file = f.name
        
        try:
            # Test with reasonable timeout
            start_time = time.time()
            
            result = duckdb_connection.execute(
                f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
            ).fetchone()
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            print(f"Processing time: {execution_time:.2f}s for {result[0]} rows")
            
            # Should complete in reasonable time
            assert execution_time < 60, f"Processing took too long: {execution_time:.2f}s"
            
        finally:
            os.unlink(temp_file)
    
    def test_system_constraint_handling(self, duckdb_connection):
        """Test handling under various system constraints."""
        print("\n=== Testing System Constraint Handling ===")
        
        resources = self.get_system_resources()
        print(f"Testing under current system load: {resources['cpu_percent']:.1f}% CPU, {resources['memory_percent']:.1f}% memory")
        
        # Create test data
        test_data = {"users": [{"id": i, "name": f"User_{i}", "data": "X" * 100} for i in range(1000)]}
        
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            json.dump(test_data, f)
            temp_file = f.name
        
        try:
            # Test multiple queries under load
            query_times = []
            
            for i in range(10):
                start_time = time.time()
                
                result = duckdb_connection.execute(
                    f"SELECT COUNT(*) FROM streaming_json_reader('{temp_file}')"
                ).fetchone()
                
                end_time = time.time()
                query_times.append(end_time - start_time)
                
                # Small delay between queries
                time.sleep(0.1)
            
            avg_time = sum(query_times) / len(query_times)
            max_time = max(query_times)
            min_time = min(query_times)
            
            print(f"Query times: avg={avg_time:.3f}s, min={min_time:.3f}s, max={max_time:.3f}s")
            
            # Performance should be consistent
            time_variance = max_time - min_time
            assert time_variance < avg_time * 2, f"High performance variance: {time_variance:.3f}s"
            
            # All queries should succeed
            assert all(isinstance(t, float) and t > 0 for t in query_times), "Some queries failed"
            
        finally:
            os.unlink(temp_file)


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
